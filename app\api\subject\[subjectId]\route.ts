import { NextRequest, NextResponse } from 'next/server';
import { getSingleSubjectServerSide } from '@/backend/utils/dataLoader';

export async function GET(
  request: NextRequest,
  { params }: { params: { subjectId: string } }
) {
  try {
    const { subjectId } = params;

    if (!subjectId) {
      return NextResponse.json(
        { error: 'subjectId is required' },
        { status: 400 }
      );
    }

    console.log(`API: جاري جلب المادة ${subjectId}...`);

    // جلب المادة مباشرة من Supabase
    const subject = await getSingleSubjectServerSide(subjectId);
    
    if (!subject) {
      return NextResponse.json(
        { error: 'Subject not found' },
        { status: 404 }
      );
    }

    console.log(`API: تم جلب المادة "${subject.name}" بنجاح`);
    
    return NextResponse.json({
      success: true,
      subject: {
        id: subject.id,
        name: subject.name,
        icon: subject.icon,
        yearId: subject.yearId,
        description: subject.description
      }
    });
  } catch (error) {
    console.error('Error fetching subject:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
