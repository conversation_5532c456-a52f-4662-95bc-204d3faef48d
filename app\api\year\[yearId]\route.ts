import { NextRequest, NextResponse } from 'next/server';
import { getSingleYearServerSide } from '@/backend/utils/dataLoader';

export async function GET(
  request: NextRequest,
  { params }: { params: { yearId: string } }
) {
  try {
    const { yearId } = params;

    if (!yearId) {
      return NextResponse.json(
        { error: 'yearId is required' },
        { status: 400 }
      );
    }

    console.log(`API: جاري جلب السنة ${yearId}...`);

    // جلب السنة مباشرة من Supabase
    const year = await getSingleYearServerSide(yearId);
    
    if (!year) {
      return NextResponse.json(
        { error: 'Year not found' },
        { status: 404 }
      );
    }

    console.log(`API: تم جلب السنة "${year.name}" بنجاح`);
    
    return NextResponse.json({
      success: true,
      year: {
        id: year.id,
        name: year.name,
        levelId: year.levelId,
        description: year.description
      }
    });
  } catch (error) {
    console.error('Error fetching year:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
